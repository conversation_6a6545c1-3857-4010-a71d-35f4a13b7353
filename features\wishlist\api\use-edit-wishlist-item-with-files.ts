import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useCurrentUserId } from "@/lib/utils";
import { client } from "@/lib/hono";
import { toast } from "sonner";

export const useEditWishlistItemWithFiles = (id?: string) => {
  const userId = useCurrentUserId();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ data, files }: { data: any; files?: FileList }) => {
      if (!userId) {
        throw new Error("User not authenticated");
      }

      if (!id) {
        throw new Error("Wishlist item ID is required");
      }

      // If no files, use regular JSON endpoint
      if (!files || files.length === 0) {
        const response = await client.api.wishlist[":id"].$patch(
          { param: { id }, json: data },
          { headers: { "X-User-ID": userId } }
        );

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "Failed to update wishlist item");
        }

        return response.json();
      }

      // Use form data endpoint for files
      const formData = new FormData();
      formData.append("data", JSON.stringify(data));

      // Add files to form data
      Array.from(files).forEach((file, index) => {
        formData.append(`file-${index}`, file);
      });

      const response = await fetch(`/api/wishlist/${id}/with-files`, {
        method: "PATCH",
        body: formData,
        headers: { "X-User-ID": userId },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to update wishlist item");
      }

      return response.json();
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["wishlist-item", { id }] });
      queryClient.invalidateQueries({ queryKey: ["wishlist"] });
      toast.success(data.message || "Wishlist item updated successfully");
    },
    onError: (error) => {
      toast.error(`Failed to update wishlist item: ${error.message}`);
    },
  });
};