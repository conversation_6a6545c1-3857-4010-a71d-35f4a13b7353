import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useCurrentUserId } from "@/lib/utils";
import { client } from "@/lib/hono";
import { toast } from "sonner";

export const useCreateWishlistWithFiles = () => {
  const userId = useCurrentUserId();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ data, files }: { data: any; files?: FileList }) => {
      if (!userId) {
        throw new Error("User not authenticated");
      }

      // If no files, use regular JSON endpoint
      if (!files || files.length === 0) {
        const response = await client.api.wishlist.$post(
          { json: data },
          { headers: { "X-User-ID": userId } }
        );

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "Failed to create wishlist item");
        }

        return response.json();
      }

      // Use form data endpoint for files
      const formData = new FormData();
      formData.append("data", JSON.stringify(data));

      // Add files to form data
      Array.from(files).forEach((file, index) => {
        formData.append(`file-${index}`, file);
      });

      const response = await fetch("/api/wishlist/with-files", {
        method: "POST",
        body: formData,
        headers: { "X-User-ID": userId },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to create wishlist item");
      }

      return response.json();
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["wishlist"] });
      toast.success(data.message || "Wishlist item created successfully");
    },
    onError: (error) => {
      toast.error(`Failed to create wishlist item: ${error.message}`);
    },
  });
};